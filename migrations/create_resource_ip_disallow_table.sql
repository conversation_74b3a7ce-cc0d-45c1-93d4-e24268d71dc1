-- 创建IP黑名单配置表
CREATE TABLE `resource_ip_disallow` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `channel_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '渠道ID',
  `ip_list` text COMMENT 'IP地址列表，CIDR格式，逗号分隔',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `is_deleted` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_channel_id` (`channel_id`) COMMENT '渠道ID唯一索引',
  KEY `idx_status` (`status`) COMMENT '状态索引',
  KEY `idx_is_deleted` (`is_deleted`) COMMENT '删除状态索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='IP黑名单配置表';

-- 插入示例数据（可选）
INSERT INTO `resource_ip_disallow` (`channel_id`, `ip_list`, `status`, `is_deleted`) VALUES
(1, '*************,10.0.0.0/8', 1, 0),
(2, '**********/12,***********/24', 1, 0);
