# 🎯 Sampling 监控系统使用指南

## ✅ 集成完成

您的 `external_ids_sync` 工具现在已完全集成了智能 sampling 监控系统！🚀

## 🎛️ Sampling 策略详解

### **根据不同模式的自动采样配置**

#### 1. **🚀 快速模式 (--fast)**
```bash
go run main.go --cmd sync-batch --config ./config/prod_read_only.ini --start-id 12900 --end-id 13000 --fast --all --verbose
```

**采样配置**:
- 📊 采样率: **10%** (只记录10%的事件，减少开销)
- 🎯 最大样本数: **50/秒** (控制输出频率)
- 📈 记录级别: **WARN+** (只记录警告和错误)
- ⏱️ 系统指标: **60秒间隔** (减少系统监控开销)

**适用场景**: 大批量处理，注重性能，网络稳定

#### 2. **🔥 激进模式 (--aggressive)**
```bash
go run main.go --cmd sync-batch --config ./config/prod_read_only.ini --start-id 12900 --end-id 13000 --aggressive --all --verbose
```

**采样配置**:
- 📊 采样率: **50%** (重点监控关键事件)
- 🎯 最大样本数: **20/秒** (减少噪音)
- 📈 记录级别: **ERROR+** (只记录错误，专注问题诊断)
- ⏱️ 系统指标: **15秒间隔** (频繁监控资源)
- 🔍 启用详细计时和堆栈跟踪

**适用场景**: API频繁超时，需要快速跳过问题项目

#### 3. **🛡️ 安全模式 (--safe)**
```bash
go run main.go --cmd sync-batch --config ./config/prod_read_only.ini --start-id 12900 --end-id 13000 --safe --all --verbose
```

**采样配置**:
- 📊 采样率: **100%** (全采样，不丢失任何信息)
- 🎯 最大样本数: **100/秒** (平衡输出)
- 📈 记录级别: **INFO+** (记录所有重要信息)
- 💾 启用内存缓冲 (防止数据丢失)
- 🔍 启用详细计时和堆栈跟踪

**适用场景**: 网络不稳定，需要完整监控，重要任务

#### 4. **🐛 调试模式 (--debug)**
```bash
go run main.go --cmd sync-batch --config ./config/prod_read_only.ini --start-id 12900 --end-id 13000 --debug --all --verbose
```

**采样配置**:
- 📊 采样率: **100%** (全采样)
- 🎯 最大样本数: **200/秒** (最大详细度)
- 📈 记录级别: **DEBUG+** (记录所有级别)
- 🌈 彩色控制台输出
- 🔍 启用所有详细信息

**适用场景**: 问题诊断，开发调试

#### 5. **📊 普通模式 (默认)**
```bash
go run main.go --cmd sync-batch --config ./config/prod_read_only.ini --start-id 12900 --end-id 13000 --all --verbose
```

**采样配置**:
- 📊 采样率: **30%** (平衡性能和监控)
- 🎯 最大样本数: **100/秒**
- 📈 记录级别: **INFO+**

**适用场景**: 日常使用，平衡性能和监控需求

## 📊 **监控输出示例**

### 启动时的采样配置信息
```
🚀 [快速模式] 采样率: 10%, 最大50样本/秒, 只记录WARN+
✅ 监控系统已初始化，采样率: 10.0%, 最大样本数: 50/秒
```

### 运行中的监控信息
```
🛡️ 防卡住模式已启用，按 Ctrl+C 可查看诊断信息
🚀 [防卡住批量] 开始同步 100 个剧集...
📈 sync_batch_progress 进度: 10/100 (10.0%)
📊 当前统计: 成功 8, 失败 2, 进度 10.0%
📈 sync_batch_progress 进度: 20/100 (20.0%)
```

### 最终监控报告
```
📊 ========== 监控报告 ==========
📈 计数器指标:
  sync_single.total_success: 85
  sync_single.total_errors: 15
  sync_single.api_success: 85
  sync_single.api_errors: 15
📊 仪表盘指标:
  sync_batch.final_success_rate: 85.00
  sync_batch.processing_rate: 0.45
  sync_batch.memory_usage_mb: 67.89
🎯 采样统计:
  总采样数: 2547
  接受采样数: 255 
  丢弃采样数: 2292
  采样接受率: 10.01%
⏱️ 运行时长: 3m45s
```

## 📁 **监控文件输出**

### 1. JSON日志文件 (./monitoring/)
```
external_ids_sync_sync-batch_20240815_143022.jsonl
```

示例内容:
```json
{
  "timestamp": "2024-08-15T14:30:45.123Z",
  "metric_type": "timing",
  "name": "sync_single.api_call",
  "value": 2.34,
  "duration": "2.34s",
  "labels": {"show_id": "12900", "api_provider": "external_ids"},
  "severity": "info",
  "context": {
    "app_name": "external_ids_sync",
    "version": "1.0.0",
    "environment": "aggressive",
    "show_id": 12900
  }
}
```

### 2. 缓冲区导出 (安全模式)
```
./monitoring/buffer_dump_20240815_143500.json
```

### 3. 实时控制台输出
彩色编码的实时监控信息，按严重程度着色

## 🎯 **监控指标详解**

### **计数器 (Counters)**
- `sync_single.total_success` - 单个同步成功次数
- `sync_single.total_errors` - 单个同步失败次数
- `sync_single.api_success` - API调用成功次数
- `sync_single.api_errors` - API调用失败次数
- `sync_batch.total_processed` - 批量处理总数

### **仪表盘 (Gauges)** 
- `sync_batch.processing_rate` - 处理速率 (项目/秒)
- `sync_batch.final_success_rate` - 最终成功率 (%)
- `sync_batch.memory_usage_mb` - 内存使用 (MB)
- `sync_single.api_duration_seconds` - API调用耗时 (秒)

### **时间测量 (Timing)**
- `sync_single.check_existing` - 检查现有记录耗时
- `sync_single.api_call` - API调用耗时
- `sync_single.get_result` - 获取结果耗时
- `sync_single.total_duration` - 单个项目总耗时

### **事件 (Events)**
- `sync_batch.started` - 批量同步开始
- `sync_single.api_retry` - API重试事件
- `sync_batch.gc_triggered` - 内存GC触发
- `sync_batch.completed` - 批量同步完成

## 🔍 **高级 Sampling 功能**

### 1. **条件采样**
系统会根据严重程度自动过滤:
```
DEBUG < INFO < WARN < ERROR < CRITICAL
```

### 2. **速率限制**
防止日志爆炸，按配置限制每秒最大样本数

### 3. **百分比采样**
在高负载时只采样指定百分比的事件

### 4. **智能缓冲**
安全模式下启用内存缓冲，防止重要事件丢失

## 📈 **性能影响分析**

### **不同采样率的性能影响**

| 模式 | 采样率 | 性能影响 | 监控详细度 | 推荐场景 |
|------|--------|----------|-----------|----------|
| 快速 | 10% | 最小 | 基础 | 大批量生产 |
| 普通 | 30% | 轻微 | 中等 | 日常使用 |
| 激进 | 50% | 中等 | 高(错误聚焦) | 问题诊断 |
| 安全 | 100% | 中等 | 最高 | 关键任务 |
| 调试 | 100% | 最高 | 最高 | 开发调试 |

## 🛠️ **自定义 Sampling 配置**

如果您需要自定义采样配置，可以修改 `initMonitoringSystem()` 函数中的参数:

```go
// 自定义采样配置示例
config.SamplingRate = 0.2              // 20%采样率
config.MaxSamplesPerSecond = 75         // 每秒最大75个样本
config.MinSeverity = monitor.SeverityInfo // 只记录INFO及以上
```

## 🚀 **立即使用您的命令**

现在您可以安全地运行您的原始命令，它将包含完整的监控和采样功能：

```bash
# 您的原始命令 + 监控功能
go run main.go --cmd sync-batch --config ./config/prod_read_only.ini --start-id 12900 --end-id 20000 --page-size 100 --aggressive --all

# 或者使用编译后的版本
./external_ids_sync_with_monitoring --cmd sync-batch --config ./config/prod_read_only.ini --start-id 12900 --end-id 20000 --page-size 100 --aggressive --all --verbose
```

## 💡 **最佳实践建议**

### 1. **生产环境推荐**
```bash
# 快速模式 - 性能优先
./external_ids_sync_with_monitoring --cmd sync-batch --config ./config/prod_read_only.ini --start-id 12900 --end-id 20000 --page-size 100 --fast --all --yes
```

### 2. **问题诊断推荐**
```bash
# 激进模式 - 错误聚焦
./external_ids_sync_with_monitoring --cmd sync-batch --config ./config/prod_read_only.ini --start-id 12900 --end-id 12950 --page-size 20 --aggressive --all --verbose
```

### 3. **完整监控推荐**
```bash
# 安全模式 - 全面监控
./external_ids_sync_with_monitoring --cmd sync-batch --config ./config/prod_read_only.ini --start-id 12900 --end-id 13100 --page-size 50 --safe --all --verbose --log-to-file
```

## 📊 **监控数据分析**

### 查看采样统计
程序结束时会显示采样效果:
- 总采样数: 实际产生的事件数
- 接受采样数: 根据策略保留的事件数
- 丢弃采样数: 为了性能而过滤的事件数
- 采样接受率: 实际记录的比例

### 分析性能瓶颈
重点关注这些指标:
- `sync_single.api_duration_seconds` - API响应时间
- `sync_batch.processing_rate` - 处理速率变化
- `sync_batch.memory_usage_mb` - 内存使用趋势
- 错误计数器的变化

**🎉 恭喜！您现在拥有了一个具备智能采样功能的高级监控系统，可以精确控制监控开销，同时获得详细的性能和错误分析数据！**
