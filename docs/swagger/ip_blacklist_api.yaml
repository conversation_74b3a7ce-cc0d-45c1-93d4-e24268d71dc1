openapi: 3.0.3
info:
  title: VLab IP黑名单管理API
  description: |
    VLab项目的IP黑名单管理系统API文档。
    
    该系统用于管理渠道级别的IP黑名单配置，当检测到请求来自黑名单IP时，会自动将该请求标记为需要强制审核状态。
    
    ## 功能特性
    - 支持单个IP地址和CIDR网段配置
    - 渠道级别的IP黑名单管理
    - 实时IP测试功能
    - Redis缓存优化
    - 完整的CRUD操作
    
    ## IP格式支持
    - 单个IP: `*************`
    - CIDR网段: `10.0.0.0/8`, `**********/12`, `***********/16`
    - 混合格式: `*************,10.0.0.0/8,**********/12`
    
  version: 1.0.0
  contact:
    name: VLab开发团队
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.vlab.com
    description: 生产环境
  - url: https://test-api.vlab.com
    description: 测试环境
  - url: http://localhost:8080
    description: 本地开发环境

security:
  - BearerAuth: []
  - CookieAuth: []

paths:
  /admin/ip-blacklist/list:
    get:
      tags:
        - IP黑名单管理
      summary: 获取IP黑名单列表
      description: 分页查询IP黑名单配置列表，支持按渠道ID和状态过滤
      operationId: getIPBlacklistList
      parameters:
        - name: page
          in: query
          description: 页码
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
            example: 1
        - name: limit
          in: query
          description: 每页数量
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
            example: 20
        - name: id
          in: query
          description: 配置ID过滤
          required: false
          schema:
            type: integer
            format: int64
            example: 1
        - name: channel_id
          in: query
          description: 渠道ID过滤
          required: false
          schema:
            type: integer
            format: int64
            example: 1
        - name: status
          in: query
          description: 状态过滤
          required: false
          schema:
            type: integer
            enum: [0, 1]
            example: 1
            description: "0-禁用, 1-启用"
      responses:
        '200':
          description: 成功获取IP黑名单列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IPBlacklistListResponse'
              example:
                code: "100000"
                msg: "成功"
                data:
                  total: 10
                  list:
                    - id: 1
                      channel_id: 1
                      channel:
                        id: 1
                        name: "默认渠道"
                      ip_list: "*************,10.0.0.0/8"
                      status: 1
                      created_at: "2024-01-01 10:00:00"
                      updated_at: "2024-01-01 10:00:00"
                runtime: 15.5
                time: 1704067200
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /admin/ip-blacklist/detail:
    get:
      tags:
        - IP黑名单管理
      summary: 获取IP黑名单详情
      description: 根据ID获取IP黑名单配置的详细信息，包括解析后的IP/CIDR列表
      operationId: getIPBlacklistDetail
      parameters:
        - name: id
          in: query
          description: IP黑名单配置ID
          required: true
          schema:
            type: integer
            format: int64
            example: 1
      responses:
        '200':
          description: 成功获取IP黑名单详情
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IPBlacklistDetailResponse'
              example:
                code: "100000"
                msg: "成功"
                data:
                  id: 1
                  channel_id: 1
                  channel:
                    id: 1
                    name: "默认渠道"
                  ip_list: "*************,10.0.0.0/8"
                  ip_cidrs: ["*************", "10.0.0.0/8"]
                  status: 1
                  created_at: "2024-01-01 10:00:00"
                  updated_at: "2024-01-01 10:00:00"
                runtime: 12.3
                time: 1704067200
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /admin/ip-blacklist/create:
    post:
      tags:
        - IP黑名单管理
      summary: 创建IP黑名单配置
      description: |
        为指定渠道创建IP黑名单配置。每个渠道只能有一个配置，如果已存在则会更新现有配置。
        
        IP列表格式说明：
        - 支持单个IP地址：*************
        - 支持CIDR网段：10.0.0.0/8
        - 支持混合格式，用逗号分隔：*************,10.0.0.0/8,**********/12
      operationId: createIPBlacklist
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IPBlacklistCreateRequest'
            example:
              channel_id: 1
              ip_list: "*************,10.0.0.0/8,**********/12"
              status: 1
      responses:
        '200':
          description: 成功创建IP黑名单配置
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
              example:
                code: "100000"
                msg: "成功"
                runtime: 25.8
                time: 1704067200
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /admin/ip-blacklist/update:
    put:
      tags:
        - IP黑名单管理
      summary: 更新IP黑名单配置
      description: |
        更新指定ID的IP黑名单配置。

        注意：更新操作会自动清除Redis缓存以确保数据一致性。
      operationId: updateIPBlacklist
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IPBlacklistUpdateRequest'
            example:
              id: 1
              channel_id: 1
              ip_list: "*************,*************,10.0.0.0/8"
              status: 1
      responses:
        '200':
          description: 成功更新IP黑名单配置
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
              example:
                code: "100000"
                msg: "成功"
                runtime: 28.2
                time: 1704067200
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /admin/ip-blacklist/delete:
    delete:
      tags:
        - IP黑名单管理
      summary: 删除IP黑名单配置
      description: |
        软删除指定ID的IP黑名单配置。删除后该配置将不再生效，但数据仍保留在数据库中。

        注意：删除操作会自动清除Redis缓存。
      operationId: deleteIPBlacklist
      parameters:
        - name: id
          in: query
          description: 要删除的IP黑名单配置ID
          required: true
          schema:
            type: integer
            format: int64
            example: 1
      responses:
        '200':
          description: 成功删除IP黑名单配置
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
              example:
                code: "100000"
                msg: "成功"
                runtime: 18.7
                time: 1704067200
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /admin/ip-blacklist/test:
    get:
      tags:
        - IP黑名单管理
      summary: 测试IP是否在黑名单中
      description: |
        测试指定IP地址是否在指定渠道的黑名单中。

        该接口用于验证IP黑名单配置是否正确，以及调试IP匹配逻辑。
      operationId: testIPBlacklist
      parameters:
        - name: channel_id
          in: query
          description: 渠道ID
          required: true
          schema:
            type: integer
            format: int64
            example: 1
        - name: ip
          in: query
          description: 要测试的IP地址
          required: true
          schema:
            type: string
            format: ipv4
            example: "*************"
            pattern: '^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
      responses:
        '200':
          description: 成功测试IP黑名单
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IPBlacklistTestResponse'
              examples:
                ip_in_blacklist:
                  summary: IP在黑名单中
                  value:
                    code: "100000"
                    msg: "成功"
                    data:
                      is_blacklisted: true
                      matched_rule: "*************"
                      message: "IP is in blacklist"
                    runtime: 8.5
                    time: 1704067200
                ip_not_in_blacklist:
                  summary: IP不在黑名单中
                  value:
                    code: "100000"
                    msg: "成功"
                    data:
                      is_blacklisted: false
                      message: "IP is not in blacklist"
                    runtime: 6.2
                    time: 1704067200
                no_config:
                  summary: 渠道无黑名单配置
                  value:
                    code: "100000"
                    msg: "成功"
                    data:
                      is_blacklisted: false
                      message: "No IP blacklist configuration found for this channel"
                    runtime: 5.1
                    time: 1704067200
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: 管理员JWT Token认证
    CookieAuth:
      type: apiKey
      in: cookie
      name: admin_session
      description: 管理员Session Cookie认证

  schemas:
    # 基础响应结构
    BaseResponse:
      type: object
      required:
        - code
        - msg
        - runtime
        - time
      properties:
        code:
          type: string
          description: 响应码，100000表示成功
          example: "100000"
        msg:
          type: string
          description: 响应消息
          example: "成功"
        runtime:
          type: number
          format: float
          description: 请求处理时间（毫秒）
          example: 15.5
        time:
          type: integer
          format: int64
          description: 服务器时间戳
          example: 1704067200

    SuccessResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
      example:
        code: "100000"
        msg: "成功"
        runtime: 15.5
        time: 1704067200

    # 渠道基础信息模型
    ChannelBase:
      type: object
      required:
        - id
        - name
      properties:
        id:
          type: integer
          format: int64
          description: 渠道ID
          example: 1
        name:
          type: string
          description: 渠道名称
          example: "默认渠道"

    # IP黑名单相关模型
    IPBlacklistItem:
      type: object
      required:
        - id
        - channel_id
        - ip_list
        - status
        - created_at
        - updated_at
      properties:
        id:
          type: integer
          format: int64
          description: 配置ID
          example: 1
        channel_id:
          type: integer
          format: int64
          description: 渠道ID（保持向后兼容）
          example: 1
        channel:
          $ref: '#/components/schemas/ChannelBase'
          description: 渠道信息对象
        ip_list:
          type: string
          description: IP地址列表，逗号分隔
          example: "*************,10.0.0.0/8"
          maxLength: 2000
        status:
          type: integer
          enum: [0, 1]
          description: 状态，0-禁用，1-启用
          example: 1
        created_at:
          type: string
          format: date-time
          description: 创建时间
          example: "2024-01-01 10:00:00"
        updated_at:
          type: string
          format: date-time
          description: 更新时间
          example: "2024-01-01 10:00:00"

    IPBlacklistDetail:
      allOf:
        - $ref: '#/components/schemas/IPBlacklistItem'
        - type: object
          properties:
            ip_cidrs:
              type: array
              items:
                type: string
              description: 解析后的IP/CIDR列表
              example: ["*************", "10.0.0.0/8"]

    IPBlacklistListData:
      type: object
      required:
        - total
        - list
      properties:
        total:
          type: integer
          format: int64
          description: 总记录数
          example: 10
        list:
          type: array
          items:
            $ref: '#/components/schemas/IPBlacklistItem'
          description: IP黑名单列表

    IPBlacklistTestData:
      type: object
      required:
        - is_blacklisted
        - message
      properties:
        is_blacklisted:
          type: boolean
          description: 是否在黑名单中
          example: true
        matched_rule:
          type: string
          description: 匹配的规则（IP或CIDR）
          example: "*************"
        message:
          type: string
          description: 测试结果描述
          example: "IP is in blacklist"

    # 请求模型
    IPBlacklistCreateRequest:
      type: object
      required:
        - channel_id
        - ip_list
      properties:
        channel_id:
          type: integer
          format: int64
          description: 渠道ID
          example: 1
          minimum: 1
        ip_list:
          type: string
          description: |
            IP地址列表，支持以下格式：
            - 单个IP：*************
            - CIDR网段：10.0.0.0/8
            - 混合格式：*************,10.0.0.0/8,**********/12
          example: "*************,10.0.0.0/8,**********/12"
          maxLength: 2000
          minLength: 1
        status:
          type: integer
          enum: [0, 1]
          description: 状态，0-禁用，1-启用，默认为1
          example: 1
          default: 1

    IPBlacklistUpdateRequest:
      type: object
      required:
        - id
        - channel_id
        - ip_list
        - status
      properties:
        id:
          type: integer
          format: int64
          description: 配置ID
          example: 1
          minimum: 1
        channel_id:
          type: integer
          format: int64
          description: 渠道ID
          example: 1
          minimum: 1
        ip_list:
          type: string
          description: IP地址列表
          example: "*************,*************,10.0.0.0/8"
          maxLength: 2000
          minLength: 1
        status:
          type: integer
          enum: [0, 1]
          description: 状态，0-禁用，1-启用
          example: 1

    # 响应模型
    IPBlacklistListResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/IPBlacklistListData'

    IPBlacklistDetailResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/IPBlacklistDetail'

    IPBlacklistTestResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/IPBlacklistTestData'

    # 错误响应模型
    ErrorResponse:
      allOf:
        - $ref: '#/components/schemas/BaseResponse'
        - type: object
          properties:
            code:
              type: string
              description: 错误码
              example: "100004"
            msg:
              type: string
              description: 错误消息
              example: "参数错误"

  responses:
    BadRequest:
      description: 请求参数错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            code: "100004"
            msg: "参数错误"
            runtime: 5.2
            time: 1704067200

    Unauthorized:
      description: 未授权，需要登录
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            code: "100005"
            msg: "token已失效"
            runtime: 2.1
            time: 1704067200

    Forbidden:
      description: 禁止访问，权限不足
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            code: "100008"
            msg: "Forbidden"
            runtime: 3.5
            time: 1704067200

    NotFound:
      description: 资源不存在
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            code: "100105"
            msg: "未找到相关数据"
            runtime: 8.3
            time: 1704067200

    InternalServerError:
      description: 服务器内部错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            code: "100002"
            msg: "系统异常，请联系客服"
            runtime: 12.7
            time: 1704067200

tags:
  - name: IP黑名单管理
    description: |
      IP黑名单管理相关接口，用于配置和管理渠道级别的IP访问控制。

      ## 主要功能
      - 创建、查询、更新、删除IP黑名单配置
      - 支持单个IP地址和CIDR网段配置
      - 实时IP测试功能
      - 自动缓存管理

      ## 权限要求
      所有接口都需要管理员登录认证和相应的操作权限。

      ## 缓存机制
      系统使用Redis缓存IP黑名单配置，缓存时间为5分钟。
      创建、更新、删除操作会自动清除缓存以确保数据一致性。

externalDocs:
  description: 完整的IP黑名单系统文档
  url: https://docs.vlab.com/ip-blacklist

# 错误码说明
x-error-codes:
  "100000": "成功"
  "100002": "系统异常，请联系客服"
  "100004": "参数错误"
  "100005": "token已失效"
  "100008": "Forbidden - 权限不足"
  "100105": "未找到相关数据"
  "101001": "仅限超级管理员可进行此操作"
  "101103": "无此权限,请联系管理员添加"

# 使用示例
x-code-samples:
  - lang: 'curl'
    label: '创建IP黑名单配置'
    source: |
      curl -X POST "https://api.vlab.com/admin/ip-blacklist/create" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
          "channel_id": 1,
          "ip_list": "*************,10.0.0.0/8,**********/12",
          "status": 1
        }'

  - lang: 'curl'
    label: '查询IP黑名单列表'
    source: |
      curl -X GET "https://api.vlab.com/admin/ip-blacklist/list?page=1&limit=20&channel_id=1" \
        -H "Authorization: Bearer YOUR_TOKEN"

  - lang: 'curl'
    label: '测试IP是否在黑名单中'
    source: |
      curl -X GET "https://api.vlab.com/admin/ip-blacklist/test?channel_id=1&ip=*************" \
        -H "Authorization: Bearer YOUR_TOKEN"

  - lang: 'javascript'
    label: 'JavaScript示例'
    source: |
      // 创建IP黑名单配置
      const response = await fetch('/admin/ip-blacklist/create', {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer YOUR_TOKEN',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          channel_id: 1,
          ip_list: '*************,10.0.0.0/8',
          status: 1
        })
      });

      const result = await response.json();
      console.log(result);

  - lang: 'python'
    label: 'Python示例'
    source: |
      import requests

      # 创建IP黑名单配置
      url = "https://api.vlab.com/admin/ip-blacklist/create"
      headers = {
          "Authorization": "Bearer YOUR_TOKEN",
          "Content-Type": "application/json"
      }
      data = {
          "channel_id": 1,
          "ip_list": "*************,10.0.0.0/8",
          "status": 1
      }

      response = requests.post(url, headers=headers, json=data)
      print(response.json())
