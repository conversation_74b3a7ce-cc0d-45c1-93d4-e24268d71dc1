{"openapi": "3.0.3", "info": {"title": "VLab IP黑名单管理API", "description": "VLab项目的IP黑名单管理系统API文档。\n\n该系统用于管理渠道级别的IP黑名单配置，当检测到请求来自黑名单IP时，会自动将该请求标记为需要强制审核状态。\n\n## 功能特性\n- 支持单个IP地址和CIDR网段配置\n- 渠道级别的IP黑名单管理\n- 实时IP测试功能\n- Redis缓存优化\n- 完整的CRUD操作\n\n## IP格式支持\n- 单个IP: `*************`\n- CIDR网段: `10.0.0.0/8`, `**********/12`, `***********/16`\n- 混合格式: `*************,10.0.0.0/8,**********/12`", "version": "1.0.0", "contact": {"name": "VLab开发团队", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}}, "servers": [{"url": "https://api.vlab.com", "description": "生产环境"}, {"url": "https://test-api.vlab.com", "description": "测试环境"}, {"url": "http://localhost:8080", "description": "本地开发环境"}], "security": [{"BearerAuth": []}, {"CookieAuth": []}], "tags": [{"name": "IP黑名单管理", "description": "IP黑名单管理相关接口，用于配置和管理渠道级别的IP访问控制。\n\n## 主要功能\n- 创建、查询、更新、删除IP黑名单配置\n- 支持单个IP地址和CIDR网段配置\n- 实时IP测试功能\n- 自动缓存管理\n\n## 权限要求\n所有接口都需要管理员登录认证和相应的操作权限。\n\n## 缓存机制\n系统使用Redis缓存IP黑名单配置，缓存时间为5分钟。\n创建、更新、删除操作会自动清除缓存以确保数据一致性。"}], "paths": {"/admin/ip-blacklist/list": {"get": {"tags": ["IP黑名单管理"], "summary": "获取IP黑名单列表", "description": "分页查询IP黑名单配置列表，支持按渠道ID和状态过滤", "operationId": "getIPBlacklistList", "parameters": [{"name": "page", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "limit", "in": "query", "description": "每页数量", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20, "example": 20}}, {"name": "id", "in": "query", "description": "配置ID过滤", "required": false, "schema": {"type": "integer", "format": "int64", "example": 1}}, {"name": "channel_id", "in": "query", "description": "渠道ID过滤", "required": false, "schema": {"type": "integer", "format": "int64", "example": 1}}, {"name": "status", "in": "query", "description": "状态过滤", "required": false, "schema": {"type": "integer", "enum": [0, 1], "example": 1, "description": "0-禁用, 1-启用"}}], "responses": {"200": {"description": "成功获取IP黑名单列表", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IPBlacklistListResponse"}, "example": {"code": "100000", "msg": "成功", "data": {"total": 10, "list": [{"id": 1, "channel_id": 1, "channel": {"id": 1, "name": "默认渠道"}, "ip_list": "*************,10.0.0.0/8", "status": 1, "created_at": "2024-01-01 10:00:00", "updated_at": "2024-01-01 10:00:00"}]}, "runtime": 15.5, "time": 1704067200}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/admin/ip-blacklist/detail": {"get": {"tags": ["IP黑名单管理"], "summary": "获取IP黑名单详情", "description": "根据ID获取IP黑名单配置的详细信息，包括解析后的IP/CIDR列表", "operationId": "getIPBlacklistDetail", "parameters": [{"name": "id", "in": "query", "description": "IP黑名单配置ID", "required": true, "schema": {"type": "integer", "format": "int64", "example": 1}}], "responses": {"200": {"description": "成功获取IP黑名单详情", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IPBlacklistDetailResponse"}, "example": {"code": "100000", "msg": "成功", "data": {"id": 1, "channel_id": 1, "channel": {"id": 1, "name": "默认渠道"}, "ip_list": "*************,10.0.0.0/8", "ip_cidrs": ["*************", "10.0.0.0/8"], "status": 1, "created_at": "2024-01-01 10:00:00", "updated_at": "2024-01-01 10:00:00"}, "runtime": 12.3, "time": 1704067200}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/admin/ip-blacklist/create": {"post": {"tags": ["IP黑名单管理"], "summary": "创建IP黑名单配置", "description": "为指定渠道创建IP黑名单配置。每个渠道只能有一个配置，如果已存在则会更新现有配置。\n\nIP列表格式说明：\n- 支持单个IP地址：*************\n- 支持CIDR网段：10.0.0.0/8\n- 支持混合格式，用逗号分隔：*************,10.0.0.0/8,**********/12", "operationId": "createIPBlacklist", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IPBlacklistCreateRequest"}, "example": {"channel_id": 1, "ip_list": "*************,10.0.0.0/8,**********/12", "status": 1}}}}, "responses": {"200": {"description": "成功创建IP黑名单配置", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}, "example": {"code": "100000", "msg": "成功", "runtime": 25.8, "time": 1704067200}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/admin/ip-blacklist/update": {"put": {"tags": ["IP黑名单管理"], "summary": "更新IP黑名单配置", "description": "更新指定ID的IP黑名单配置。\n\n注意：更新操作会自动清除Redis缓存以确保数据一致性。", "operationId": "updateIPBlacklist", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IPBlacklistUpdateRequest"}, "example": {"id": 1, "channel_id": 1, "ip_list": "*************,*************,10.0.0.0/8", "status": 1}}}}, "responses": {"200": {"description": "成功更新IP黑名单配置", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}, "example": {"code": "100000", "msg": "成功", "runtime": 28.2, "time": 1704067200}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/admin/ip-blacklist/delete": {"delete": {"tags": ["IP黑名单管理"], "summary": "删除IP黑名单配置", "description": "软删除指定ID的IP黑名单配置。删除后该配置将不再生效，但数据仍保留在数据库中。\n\n注意：删除操作会自动清除Redis缓存。", "operationId": "deleteIPBlacklist", "parameters": [{"name": "id", "in": "query", "description": "要删除的IP黑名单配置ID", "required": true, "schema": {"type": "integer", "format": "int64", "example": 1}}], "responses": {"200": {"description": "成功删除IP黑名单配置", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}, "example": {"code": "100000", "msg": "成功", "runtime": 18.7, "time": 1704067200}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}}, "components": {"securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "管理员JWT Token认证"}, "CookieAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "cookie", "name": "admin_session", "description": "管理员Session Cookie认证"}}, "schemas": {"BaseResponse": {"type": "object", "required": ["code", "msg", "runtime", "time"], "properties": {"code": {"type": "string", "description": "响应码，100000表示成功", "example": "100000"}, "msg": {"type": "string", "description": "响应消息", "example": "成功"}, "runtime": {"type": "number", "format": "float", "description": "请求处理时间（毫秒）", "example": 15.5}, "time": {"type": "integer", "format": "int64", "description": "服务器时间戳", "example": 1704067200}}}, "SuccessResponse": {"allOf": [{"$ref": "#/components/schemas/BaseResponse"}], "example": {"code": "100000", "msg": "成功", "runtime": 15.5, "time": 1704067200}}, "ChannelBase": {"type": "object", "required": ["id", "name"], "properties": {"id": {"type": "integer", "format": "int64", "description": "渠道ID", "example": 1}, "name": {"type": "string", "description": "渠道名称", "example": "默认渠道"}}}}}}