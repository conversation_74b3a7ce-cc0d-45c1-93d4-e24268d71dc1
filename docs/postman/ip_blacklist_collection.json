{"info": {"name": "VLab IP黑名单管理API", "description": "VLab项目IP黑名单管理系统的完整API集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_postman_id": "ip-blacklist-api-collection", "version": {"major": 1, "minor": 0, "patch": 0}}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string", "description": "API基础URL"}, {"key": "adminToken", "value": "YOUR_ADMIN_TOKEN", "type": "string", "description": "管理员认证Token"}, {"key": "channelId", "value": "1", "type": "string", "description": "测试用渠道ID"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{adminToken}}", "type": "string"}]}, "item": [{"name": "IP黑名单管理", "description": "IP黑名单管理相关接口", "item": [{"name": "获取IP黑名单列表", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{baseUrl}}/admin/ip-blacklist/list?page=1&limit=20&channel_id={{channelId}}", "host": ["{{baseUrl}}"], "path": ["admin", "ip-blacklist", "list"], "query": [{"key": "page", "value": "1", "description": "页码"}, {"key": "limit", "value": "20", "description": "每页数量"}, {"key": "channel_id", "value": "{{channelId}}", "description": "渠道ID过滤"}, {"key": "status", "value": "1", "description": "状态过滤", "disabled": true}]}, "description": "分页查询IP黑名单配置列表，支持按渠道ID和状态过滤"}, "response": [{"name": "成功响应", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/admin/ip-blacklist/list?page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["admin", "ip-blacklist", "list"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"code\": \"100000\",\n  \"msg\": \"成功\",\n  \"data\": {\n    \"total\": 10,\n    \"list\": [\n      {\n        \"id\": 1,\n        \"channel_id\": 1,\n        \"channel\": {\n          \"id\": 1,\n          \"name\": \"默认渠道\"\n        },\n        \"ip_list\": \"*************,10.0.0.0/8\",\n        \"status\": 1,\n        \"created_at\": \"2024-01-01 10:00:00\",\n        \"updated_at\": \"2024-01-01 10:00:00\"\n      }\n    ]\n  },\n  \"runtime\": 15.5,\n  \"time\": 1704067200\n}"}]}, {"name": "获取IP黑名单详情", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{baseUrl}}/admin/ip-blacklist/detail?id=1", "host": ["{{baseUrl}}"], "path": ["admin", "ip-blacklist", "detail"], "query": [{"key": "id", "value": "1", "description": "IP黑名单配置ID"}]}, "description": "根据ID获取IP黑名单配置的详细信息，包括解析后的IP/CIDR列表"}, "response": [{"name": "成功响应", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/admin/ip-blacklist/detail?id=1", "host": ["{{baseUrl}}"], "path": ["admin", "ip-blacklist", "detail"], "query": [{"key": "id", "value": "1"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"code\": \"100000\",\n  \"msg\": \"成功\",\n  \"data\": {\n    \"id\": 1,\n    \"channel_id\": 1,\n    \"channel\": {\n      \"id\": 1,\n      \"name\": \"默认渠道\"\n    },\n    \"ip_list\": \"*************,10.0.0.0/8\",\n    \"ip_cidrs\": [\"*************\", \"10.0.0.0/8\"],\n    \"status\": 1,\n    \"created_at\": \"2024-01-01 10:00:00\",\n    \"updated_at\": \"2024-01-01 10:00:00\"\n  },\n  \"runtime\": 12.3,\n  \"time\": 1704067200\n}"}]}, {"name": "创建IP黑名单配置", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"channel_id\": {{channelId}},\n  \"ip_list\": \"*************,10.0.0.0/8,**********/12\",\n  \"status\": 1\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/admin/ip-blacklist/create", "host": ["{{baseUrl}}"], "path": ["admin", "ip-blacklist", "create"]}, "description": "为指定渠道创建IP黑名单配置。每个渠道只能有一个配置，如果已存在则会更新现有配置。"}, "response": [{"name": "成功响应", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"channel_id\": 1,\n  \"ip_list\": \"*************,10.0.0.0/8,**********/12\",\n  \"status\": 1\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/admin/ip-blacklist/create", "host": ["{{baseUrl}}"], "path": ["admin", "ip-blacklist", "create"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"code\": \"100000\",\n  \"msg\": \"成功\",\n  \"runtime\": 25.8,\n  \"time\": 1704067200\n}"}]}]}]}