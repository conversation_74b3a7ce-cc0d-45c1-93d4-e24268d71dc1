# MarketBannerList IP黑名单强制审核功能修复

## 问题描述

在IP黑名单系统的实现中，发现 `MarketBannerList` 接口虽然在路由层添加了IP黑名单检查中间件 `mw.IPBlacklistCheck()`，但在Service层的业务逻辑中没有应用强制审核机制。

### 问题分析

**路由配置**（已正确）:
```go
// router/show.go
bn := e.Group("/market/banner/", mw.ReplayProtection(), mw.CheckUser(), mw.IPBlacklistCheck())
{
    bn.GET("/list", show.MarketBannerList) // market/banner/list 轮播列表
}
```

**Service层问题**（修复前）:
```go
// app/service/show/market.go - MarketBannerList方法
// 只有版本控制逻辑，缺少IP黑名单强制审核逻辑
if t := reqCtx.VersionModel; t != nil {
    switch t.Status {
    case uint32(dbs.StatusEnable): // 版本已上线，正常返回
    case uint32(dbs.StatusDisable): // 版本已下线，返回空
        return res, nil
    case uint32(dbs.StatusAuditIng): // 版本审核中，显示审核横幅
        filter.Status = dbs.StatusAuditIng
    }
}
```

**对比其他接口**（正确实现）:
```go
// app/service/show/show.go - applyVersionFilter方法
forceAudit := middleware.GetForceAuditStatus(ctx)
if t := reqCtx.VersionModel; t != nil {
    switch t.Status {
    case uint32(dbs.StatusEnable):
        // 如果IP在黑名单中，即使版本已上线也要强制进入审核模式
        if forceAudit {
            // 强制审核逻辑
        }
    // ... 其他状态处理
    }
}
```

## 修复方案

### 1. 添加必要的导入

```go
import (
    // ... 其他导入
    "vlab/app/middleware"        // 新增：用于获取强制审核状态
    "github.com/sirupsen/logrus" // 新增：用于日志记录
)
```

### 2. 修改版本过滤逻辑

**修复前**:
```go
// 应用版本过滤逻辑 - 复用剧集服务的版本控制逻辑
if t := reqCtx.VersionModel; t != nil {
    switch t.Status {
    case uint32(dbs.StatusEnable): // 若该版本已上线, 则需要正常返回, 不需要过滤
    case uint32(dbs.StatusDisable): // 若该版本已下线, 则需要返回空
        return res, nil
    case uint32(dbs.StatusAuditIng): // 若该版本在审核中, 则需要直接展示审核横幅
        filter.Status = dbs.StatusAuditIng
    }
}
```

**修复后**:
```go
// 检查是否需要强制审核（IP黑名单检查结果）
forceAudit := middleware.GetForceAuditStatus(ctx)

// 应用版本过滤逻辑 - 复用剧集服务的版本控制逻辑，并支持IP黑名单强制审核
if t := reqCtx.VersionModel; t != nil {
    switch t.Status {
    case uint32(dbs.StatusEnable): // 若该版本已上线, 则需要正常返回, 不需要过滤
        // 如果IP在黑名单中，即使版本已上线也要强制进入审核模式
        if forceAudit {
            log.Ctx(ctx).WithFields(logrus.Fields{
                "channel_id":  reqCtx.ChannelID,
                "version_id":  reqCtx.VersionID,
                "force_audit": forceAudit,
            }).Info("Force audit mode enabled for banner list due to IP blacklist")
            filter.Status = dbs.StatusAuditIng
        }
    case uint32(dbs.StatusDisable): // 若该版本已下线, 则需要返回空
        return res, nil
    case uint32(dbs.StatusAuditIng): // 若该版本在审核中, 则需要直接展示审核横幅
        filter.Status = dbs.StatusAuditIng
    }
} else if forceAudit {
    // 如果没有版本信息但需要强制审核，也要进入审核模式
    log.Ctx(ctx).WithFields(logrus.Fields{
        "channel_id":  reqCtx.ChannelID,
        "force_audit": forceAudit,
    }).Info("Force audit mode enabled for banner list due to IP blacklist (no version info)")
    filter.Status = dbs.StatusAuditIng
}
```

## 修复效果

### 1. 功能完整性

**修复前**:
- ❌ IP黑名单用户仍能看到正常横幅内容
- ❌ 强制审核机制不生效
- ❌ 与其他接口行为不一致

**修复后**:
- ✅ IP黑名单用户自动进入强制审核模式
- ✅ 只显示审核状态的横幅内容
- ✅ 与其他接口行为保持一致

### 2. 业务逻辑矩阵

| 版本状态 | 用户类型 | 修复前行为 | 修复后行为 | 说明 |
|----------|----------|------------|------------|------|
| 已上线 | 正常用户 | 显示正常横幅 | 显示正常横幅 | ✅ 无变化 |
| 已上线 | 黑名单用户 | 显示正常横幅 ❌ | 显示审核横幅 ✅ | 🔧 修复 |
| 审核中 | 正常用户 | 显示审核横幅 | 显示审核横幅 | ✅ 无变化 |
| 审核中 | 黑名单用户 | 显示审核横幅 | 显示审核横幅 | ✅ 无变化 |
| 已下线 | 任何用户 | 返回空结果 | 返回空结果 | ✅ 无变化 |
| 无版本 | 正常用户 | 显示正常横幅 | 显示正常横幅 | ✅ 无变化 |
| 无版本 | 黑名单用户 | 显示正常横幅 ❌ | 显示审核横幅 ✅ | 🔧 修复 |

### 3. 日志记录

修复后会记录强制审核模式的触发情况：

```
INFO Force audit mode enabled for banner list due to IP blacklist
  channel_id=1 version_id=1 force_audit=true

INFO Force audit mode enabled for banner list due to IP blacklist (no version info)
  channel_id=1 force_audit=true
```

## 测试验证

### 1. 单元测试场景

```go
func TestMarketBannerListIPBlacklist(t *testing.T) {
    testCases := []struct {
        name           string
        versionStatus  uint32
        isBlacklisted  bool
        expectedStatus uint32
    }{
        {"正常用户_版本已上线", dbs.StatusEnable, false, dbs.StatusEnable},
        {"黑名单用户_版本已上线", dbs.StatusEnable, true, dbs.StatusAuditIng},
        {"正常用户_版本审核中", dbs.StatusAuditIng, false, dbs.StatusAuditIng},
        {"黑名单用户_版本审核中", dbs.StatusAuditIng, true, dbs.StatusAuditIng},
    }
    
    for _, tc := range testCases {
        // 测试逻辑
    }
}
```

### 2. 集成测试

```bash
# 正常用户访问
curl -X GET "http://localhost:8080/api/market/banner/list" \
     -H "Channel-Key: test_channel" \
     -H "User-Token: normal_user_token"

# 黑名单用户访问
curl -X GET "http://localhost:8080/api/market/banner/list" \
     -H "Channel-Key: test_channel" \
     -H "User-Token: blacklist_user_token"
```

### 3. 验证要点

- [x] 黑名单用户只能看到审核状态的横幅
- [x] 正常用户行为不受影响
- [x] 版本控制逻辑保持不变
- [x] 日志记录正确
- [x] 性能无明显影响

## 兼容性保证

### 1. 向后兼容

- ✅ 正常用户的访问体验完全不变
- ✅ 现有的版本控制逻辑保持不变
- ✅ API接口签名和响应格式不变
- ✅ 数据库查询逻辑基本不变（只是过滤条件调整）

### 2. 性能影响

- ✅ 只增加一次 `middleware.GetForceAuditStatus(ctx)` 调用
- ✅ 无额外的数据库查询
- ✅ 日志记录开销极小
- ✅ 内存使用无明显增加

## 相关接口对比

### 已正确实现IP黑名单强制审核的接口

1. **ShowList** (`/api/show/list`)
2. **ShowSearch** (`/api/show/search`)
3. **ShowRecommendList** (`/api/show/recommend/list`)
4. **ShowAssignList** (`/api/show/assign/list`)
5. **ShowPopularList** (`/api/show/popular/list`)

### 本次修复的接口

6. **MarketBannerList** (`/api/market/banner/list`) ✅ 已修复

## 总结

本次修复解决了 `MarketBannerList` 接口在IP黑名单系统中的功能缺失问题，确保了：

1. **功能完整性**: IP黑名单用户无法绕过审核机制查看未审核的横幅内容
2. **行为一致性**: 与其他内容接口的强制审核行为保持一致
3. **安全性**: 加强了内容访问控制，防止不当内容展示
4. **可维护性**: 代码逻辑与其他接口保持一致，便于维护

修复后，IP黑名单系统在所有相关接口中都能正确工作，为内容安全提供了完整的保障。
