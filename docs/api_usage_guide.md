# IP黑名单管理API使用指南

## 概述

本文档提供VLab IP黑名单管理API的详细使用指南，包括认证方式、接口调用示例、错误处理等。

## 认证方式

所有IP黑名单管理接口都需要管理员认证，支持以下两种认证方式：

### 1. Bearer Token认证（推荐）

```bash
curl -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
     -H "Content-Type: application/json" \
     "https://api.vlab.com/admin/ip-blacklist/list"
```

### 2. Cookie认证

```bash
curl -H "Cookie: admin_session=YOUR_SESSION_COOKIE" \
     -H "Content-Type: application/json" \
     "https://api.vlab.com/admin/ip-blacklist/list"
```

## 接口调用示例

### 1. 获取IP黑名单列表

**基础查询**
```bash
curl -X GET "https://api.vlab.com/admin/ip-blacklist/list?page=1&limit=20" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

**带过滤条件的查询**
```bash
curl -X GET "https://api.vlab.com/admin/ip-blacklist/list?page=1&limit=20&channel_id=1&status=1" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

**响应示例**
```json
{
  "code": "100000",
  "msg": "成功",
  "data": {
    "total": 10,
    "list": [
      {
        "id": 1,
        "channel_id": 1,
        "channel": {
          "id": 1,
          "name": "默认渠道"
        },
        "ip_list": "*************,10.0.0.0/8",
        "status": 1,
        "created_at": "2024-01-01 10:00:00",
        "updated_at": "2024-01-01 10:00:00"
      }
    ]
  },
  "runtime": 15.5,
  "time": 1704067200
}
```

**字段说明**
- `channel_id`: 渠道ID（保持向后兼容）
- `channel`: 渠道信息对象，包含渠道ID和名称

### 2. 创建IP黑名单配置

**单个IP地址**
```bash
curl -X POST "https://api.vlab.com/admin/ip-blacklist/create" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "channel_id": 1,
       "ip_list": "*************",
       "status": 1
     }'
```

**CIDR网段**
```bash
curl -X POST "https://api.vlab.com/admin/ip-blacklist/create" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "channel_id": 1,
       "ip_list": "10.0.0.0/8",
       "status": 1
     }'
```

**混合格式**
```bash
curl -X POST "https://api.vlab.com/admin/ip-blacklist/create" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "channel_id": 1,
       "ip_list": "*************,10.0.0.0/8,**********/12",
       "status": 1
     }'
```

### 3. 更新IP黑名单配置

```bash
curl -X PUT "https://api.vlab.com/admin/ip-blacklist/update" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "id": 1,
       "channel_id": 1,
       "ip_list": "*************,*************,10.0.0.0/8",
       "status": 1
     }'
```

### 4. 删除IP黑名单配置

```bash
curl -X DELETE "https://api.vlab.com/admin/ip-blacklist/delete?id=1" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

### 5. 测试IP是否在黑名单中

```bash
curl -X GET "https://api.vlab.com/admin/ip-blacklist/test?channel_id=1&ip=*************" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

**响应示例（IP在黑名单中）**
```json
{
  "code": "100000",
  "msg": "成功",
  "data": {
    "is_blacklisted": true,
    "matched_rule": "*************",
    "message": "IP is in blacklist"
  },
  "runtime": 8.5,
  "time": 1704067200
}
```

## IP格式说明

### 支持的IP格式

1. **单个IP地址**
   ```
   *************
   ```

2. **CIDR网段**
   ```
   10.0.0.0/8        # 10.0.0.0 - **************
   **********/12     # ********** - **************
   ***********/16    # *********** - ***************
   ```

3. **混合格式（逗号分隔）**
   ```
   *************,10.0.0.0/8,**********/12
   ```

### 常用网段示例

- **私有网络**：`10.0.0.0/8,**********/12,***********/16`
- **本地网络**：`*********/8`
- **链路本地**：`***********/16`

## 错误处理

### 常见错误码

| 错误码 | 错误信息 | 说明 | 解决方案 |
|--------|----------|------|----------|
| 100000 | 成功 | 请求成功 | - |
| 100004 | 参数错误 | 请求参数不正确 | 检查参数格式和必填项 |
| 100005 | token已失效 | 认证token过期 | 重新获取管理员token |
| 100008 | Forbidden | 权限不足 | 确认管理员权限 |
| 100105 | 未找到相关数据 | 资源不存在 | 确认资源ID是否正确 |

### 错误响应示例

```json
{
  "code": "100004",
  "msg": "参数错误",
  "runtime": 5.2,
  "time": 1704067200
}
```

### IP格式验证错误

```json
{
  "code": "100004",
  "msg": "Invalid IP address: invalid-ip",
  "runtime": 3.1,
  "time": 1704067200
}
```

## 最佳实践

### 1. 分页查询

- 建议每页限制在50条以内
- 使用合适的过滤条件减少数据量

```bash
# 推荐
curl "https://api.vlab.com/admin/ip-blacklist/list?page=1&limit=20&channel_id=1"

# 不推荐
curl "https://api.vlab.com/admin/ip-blacklist/list?limit=1000"
```

### 2. IP配置管理

- 使用CIDR网段而不是大量单个IP
- 定期清理无效的IP配置
- 测试IP配置是否生效

```bash
# 推荐：使用CIDR网段
"ip_list": "10.0.0.0/8"

# 不推荐：大量单个IP
"ip_list": "********,********,********,..."
```

### 3. 错误重试

- 对于5xx错误，可以进行重试
- 对于4xx错误，应该检查请求参数
- 使用指数退避策略

```bash
# 示例：带重试的脚本
for i in {1..3}; do
  response=$(curl -s -w "%{http_code}" "https://api.vlab.com/admin/ip-blacklist/list")
  if [[ "${response: -3}" == "200" ]]; then
    echo "Success"
    break
  else
    echo "Retry $i"
    sleep $((2**i))
  fi
done
```

## 工具集成

### 1. Postman集合

导入提供的Postman集合文件：`docs/postman/ip_blacklist_collection.json`

### 2. Swagger UI

访问Swagger UI查看交互式API文档：
- 本地：`http://localhost:8080/swagger-ui/`
- 测试环境：`https://test-api.vlab.com/swagger-ui/`

### 3. 代码生成

使用OpenAPI Generator生成客户端代码：

```bash
# 生成JavaScript客户端
openapi-generator-cli generate \
  -i docs/swagger/ip_blacklist_api.yaml \
  -g javascript \
  -o ./generated/javascript-client

# 生成Python客户端
openapi-generator-cli generate \
  -i docs/swagger/ip_blacklist_api.yaml \
  -g python \
  -o ./generated/python-client
```

## 监控和调试

### 1. 请求追踪

每个响应都包含`Trace-Id`头部，用于请求追踪：

```bash
curl -v "https://api.vlab.com/admin/ip-blacklist/list" \
     -H "Authorization: Bearer YOUR_TOKEN"

# 响应头中包含：
# Trace-Id: abc123def456
```

### 2. 性能监控

关注响应中的`runtime`字段，监控API性能：

```json
{
  "code": "100000",
  "msg": "成功",
  "runtime": 15.5,  // 请求处理时间（毫秒）
  "time": 1704067200
}
```

### 3. 缓存状态

- 创建、更新、删除操作会自动清除缓存
- 查询操作优先使用缓存数据
- 缓存时间：5分钟

## 安全注意事项

1. **Token安全**：妥善保管管理员token，定期更换
2. **HTTPS**：生产环境必须使用HTTPS
3. **权限控制**：确保只有授权人员可以访问管理接口
4. **日志审计**：所有操作都会记录到管理员操作日志中
5. **IP验证**：严格验证IP格式，防止注入攻击
