# IP黑名单检查中间件

## 功能描述

IP黑名单检查中间件用于强制审核特定IP的请求。当检测到请求来自黑名单IP时，会自动将该请求标记为需要强制审核状态，即使版本已上线也会进入审核模式。

## 技术实现

### 数据库表结构

```sql
CREATE TABLE `resource_ip_disallow` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `channel_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '渠道ID',
  `ip_list` text COMMENT 'IP地址列表，CIDR格式，逗号分隔',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `is_deleted` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_channel_id` (`channel_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='IP黑名单配置表';
```

### 核心组件

1. **数据模型** (`app/dao/resource_ip_disallow/model.go`)
   - 支持CIDR格式的IP地址匹配
   - 支持单个IP地址和IP段的混合配置
   - 每个渠道仅有一行配置记录

2. **数据访问层** (`app/dao/resource_ip_disallow/`)
   - 提供基本的CRUD操作
   - 支持Redis缓存以提高性能
   - 根据渠道ID快速查询配置

3. **中间件** (`app/middleware/ip_blacklist.go`)
   - 获取客户端真实IP地址
   - 检查IP是否在对应渠道的黑名单中
   - 设置强制审核状态标志位

4. **业务逻辑集成** (`app/service/show/show.go`)
   - 修改`applyVersionFilter`函数
   - 支持强制审核模式
   - 即使版本已上线也会进入审核状态

## 使用方式

### 1. 路由集成

在需要IP黑名单检查的路由组中添加中间件：

```go
// 在router/show.go中集成
func loadShowApi(e *gin.RouterGroup) {
    e.GET("/class/list", mw.ReplayProtection(), show.ClassList)

    // 添加IP黑名单检查中间件
    sh := e.Group("/show/", mw.ReplayProtection(), mw.CheckUser(), mw.IPBlacklistCheck())
    {
        sh.GET("/list", show.ShowList)
        sh.GET("/detail", show.ShowDetail)
        sh.GET("/search", show.ShowSearch)
        sh.GET("/recommend/list", show.ShowRecommendList)
        sh.GET("/popular/list", show.ShowPopularList)
        sh.GET("/assign/list", show.ShowAssignList)
    }
}
```

### 2. 数据配置

通过管理后台或API接口配置IP黑名单：

```go
// 示例：为渠道ID=1配置IP黑名单
ipDisallowConfig := &resource_ip_disallow.Model{
    ChannelID: 1,
    IPList:    "*************,10.0.0.0/8,**********/12", // 支持单个IP和CIDR格式
    Status:    1, // 启用
}

err := resource_ip_disallow.GetRepo().CreateOrUpdate(ctx, ipDisallowConfig)
```

### 3. 状态检查

在业务逻辑中检查强制审核状态：

```go
// 检查是否需要强制审核
forceAudit := middleware.GetForceAuditStatus(ctx)
if forceAudit {
    // 执行强制审核相关逻辑
    log.Ctx(ctx).Info("Request is in force audit mode due to IP blacklist")
}
```

## IP地址格式支持

### 支持的格式

1. **单个IP地址**：`*************`
2. **CIDR网段**：`10.0.0.0/8`, `**********/12`, `***********/16`
3. **混合配置**：`*************,10.0.0.0/8,**********/12`

### 配置示例

```
# 阻止特定IP
*************

# 阻止整个C类网段
***********/24

# 阻止多个IP和网段
*************,10.0.0.0/8,**********/12,***********/24
```

## 性能优化

### 缓存机制

- 使用Redis缓存IP黑名单配置，减少数据库查询
- 缓存时间：5分钟
- 缓存键：`vlab:cache:list:ipDisallow`

### 错误处理

- 如果缓存或数据库查询失败，不会阻断正常请求
- 记录详细的错误日志便于排查问题
- 采用降级策略确保服务可用性

## 监控和日志

### 关键日志

1. **IP黑名单命中**：
```
INFO Force audit mode enabled due to IP blacklist channel_id=1 client_ip=*************
```

2. **缓存失败降级**：
```
WARN Failed to get IP blacklist from cache, querying database
```

3. **配置错误**：
```
ERROR Failed to check IP blacklist channel_id=1 client_ip=*************
```

### 监控指标

- IP黑名单命中率
- 缓存命中率
- 查询响应时间
- 错误率统计

## 管理API接口

### 接口列表

| 方法 | 路径 | 说明 | 权限要求 |
|------|------|------|----------|
| GET | `/admin/ip-blacklist/list` | 分页查询IP黑名单列表 | 管理员登录 + 权限验证 |
| GET | `/admin/ip-blacklist/detail` | 获取IP黑名单详情 | 管理员登录 + 权限验证 |
| POST | `/admin/ip-blacklist/create` | 创建IP黑名单配置 | 管理员登录 + 权限验证 |
| PUT | `/admin/ip-blacklist/update` | 更新IP黑名单配置 | 管理员登录 + 权限验证 |
| DELETE | `/admin/ip-blacklist/delete` | 删除IP黑名单配置 | 管理员登录 + 权限验证 |
| GET | `/admin/ip-blacklist/test` | 测试IP是否在黑名单中 | 管理员登录 + 权限验证 |

### 接口详情

#### 1. 获取IP黑名单列表

**请求**
```http
GET /admin/ip-blacklist/list?page=1&limit=20&channel_id=1&status=1
```

**参数**
- `page` (int, 可选): 页码，默认1
- `limit` (int, 可选): 每页数量，默认20，最大100
- `channel_id` (uint64, 可选): 渠道ID过滤
- `status` (uint32, 可选): 状态过滤，1-启用，0-禁用

**响应**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 10,
    "list": [
      {
        "id": 1,
        "channel_id": 1,
        "ip_list": "*************,10.0.0.0/8",
        "status": 1,
        "created_at": "2024-01-01 10:00:00",
        "updated_at": "2024-01-01 10:00:00"
      }
    ]
  }
}
```

#### 2. 获取IP黑名单详情

**请求**
```http
GET /admin/ip-blacklist/detail?id=1
```

**参数**
- `id` (uint64, 必填): IP黑名单配置ID

**响应**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "channel_id": 1,
    "ip_list": "*************,10.0.0.0/8",
    "ip_cidrs": ["*************", "10.0.0.0/8"],
    "status": 1,
    "created_at": "2024-01-01 10:00:00",
    "updated_at": "2024-01-01 10:00:00"
  }
}
```

#### 3. 创建IP黑名单配置

**请求**
```http
POST /admin/ip-blacklist/create
Content-Type: application/json

{
  "channel_id": 1,
  "ip_list": "*************,10.0.0.0/8,**********/12",
  "status": 1
}
```

**参数**
- `channel_id` (uint64, 必填): 渠道ID
- `ip_list` (string, 必填): IP地址列表，逗号分隔，最大2000字符
- `status` (uint32, 可选): 状态，1-启用，0-禁用，默认1

**响应**
```json
{
  "code": 200,
  "message": "success"
}
```

#### 4. 更新IP黑名单配置

**请求**
```http
PUT /admin/ip-blacklist/update
Content-Type: application/json

{
  "id": 1,
  "channel_id": 1,
  "ip_list": "*************,10.0.0.0/8",
  "status": 0
}
```

**参数**
- `id` (uint64, 必填): 配置ID
- `channel_id` (uint64, 必填): 渠道ID
- `ip_list` (string, 必填): IP地址列表
- `status` (uint32, 必填): 状态

#### 5. 删除IP黑名单配置

**请求**
```http
DELETE /admin/ip-blacklist/delete?id=1
```

**参数**
- `id` (uint64, 必填): 配置ID

#### 6. 测试IP是否在黑名单中

**请求**
```http
GET /admin/ip-blacklist/test?channel_id=1&ip=*************
```

**参数**
- `channel_id` (uint64, 必填): 渠道ID
- `ip` (string, 必填): 要测试的IP地址

**响应**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "is_blacklisted": true,
    "matched_rule": "*************",
    "message": "IP is in blacklist"
  }
}
```

## 注意事项

1. **IP获取准确性**：确保能正确获取客户端真实IP，考虑代理和负载均衡器的影响
2. **性能影响**：每个请求都会执行IP检查，需要优化查询性能
3. **配置管理**：建议通过管理后台统一管理IP黑名单配置
4. **日志记录**：重要操作需要记录详细日志便于审计和排查
5. **缓存一致性**：配置更新后需要及时清除缓存
6. **权限控制**：所有管理接口都需要管理员登录和权限验证
7. **操作日志**：所有CUD操作都会记录到管理员操作日志中
