# IP黑名单管理系统

## 概述

本系统为vlab项目实现了完整的IP黑名单管理功能，包括中间件、管理接口、缓存优化等。当检测到请求来自黑名单IP时，会自动将该请求标记为需要强制审核状态。

## 功能特性

### 核心功能
- ✅ IP黑名单检查中间件
- ✅ 支持单个IP和CIDR网段匹配
- ✅ 强制审核状态设置
- ✅ Redis缓存优化
- ✅ 完整的管理API接口
- ✅ 详细的日志记录

### 管理功能
- ✅ 分页查询IP黑名单列表
- ✅ 创建/更新/删除IP黑名单配置
- ✅ IP黑名单测试功能
- ✅ 权限控制和操作日志
- ✅ 参数验证和错误处理

## 文件结构

```
├── app/
│   ├── dao/resource_ip_disallow/          # 数据访问层
│   │   ├── model.go                       # 数据模型
│   │   ├── interface.go                   # 接口定义
│   │   ├── repo.go                        # 数据库操作
│   │   ├── redis.go                       # 缓存操作
│   │   └── model_test.go                  # 单元测试
│   ├── dto/admin/
│   │   └── ip_blacklist.go                # 请求/响应DTO
│   ├── handler/admin/
│   │   ├── ip_blacklist.go                # 管理接口处理器
│   │   └── ip_blacklist_test.go           # 接口测试
│   ├── middleware/
│   │   ├── ip_blacklist.go                # IP黑名单中间件
│   │   └── ip_blacklist_test.go           # 中间件测试
│   └── service/show/
│       └── show.go                        # 业务逻辑集成（已修改）
├── router/
│   ├── admin.go                           # 管理路由（已修改）
│   └── show.go                            # 业务路由（已修改）
├── pkg/redis/
│   └── keys_cache.go                      # 缓存键定义（已修改）
├── migrations/
│   └── create_resource_ip_disallow_table.sql  # 数据库迁移
├── docs/
│   └── ip_blacklist_middleware.md         # 详细文档
├── examples/
│   └── ip_blacklist_example.go            # 使用示例
└── README_IP_BLACKLIST.md                 # 本文件
```

## 快速开始

### 1. 数据库初始化

```sql
-- 执行数据库迁移
source migrations/create_resource_ip_disallow_table.sql
```

### 2. 中间件集成

在需要IP黑名单检查的路由中添加中间件：

```go
// router/show.go
sh := e.Group("/show/", mw.ReplayProtection(), mw.CheckUser(), mw.IPBlacklistCheck())
```

### 3. 管理接口使用

```bash
# 创建IP黑名单配置
curl -X POST http://localhost:8080/admin/ip-blacklist/create \
  -H "Content-Type: application/json" \
  -d '{
    "channel_id": 1,
    "ip_list": "*************,10.0.0.0/8,**********/12",
    "status": 1
  }'

# 查询IP黑名单列表
curl "http://localhost:8080/admin/ip-blacklist/list?page=1&limit=20"

# 测试IP是否在黑名单中
curl "http://localhost:8080/admin/ip-blacklist/test?channel_id=1&ip=*************"
```

## API接口

### 管理接口列表

| 方法 | 路径 | 说明 |
|------|------|------|
| GET | `/admin/ip-blacklist/list` | 分页查询IP黑名单列表 |
| GET | `/admin/ip-blacklist/detail` | 获取IP黑名单详情 |
| POST | `/admin/ip-blacklist/create` | 创建IP黑名单配置 |
| PUT | `/admin/ip-blacklist/update` | 更新IP黑名单配置 |
| DELETE | `/admin/ip-blacklist/delete` | 删除IP黑名单配置 |
| GET | `/admin/ip-blacklist/test` | 测试IP是否在黑名单中 |

### 权限要求

所有管理接口都需要：
- 管理员登录认证 (`mw.CheckAccountLogin()`)
- 权限验证 (`mw.CheckAccountAuth()`)
- 操作日志记录 (`mw.AdminOperationLog()`)

## 技术实现

### 数据库设计

```sql
CREATE TABLE `resource_ip_disallow` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `channel_id` bigint(20) unsigned NOT NULL DEFAULT '0',
  `ip_list` text COMMENT 'IP地址列表，CIDR格式，逗号分隔',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1',
  `is_deleted` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_channel_id` (`channel_id`)
);
```

### 缓存策略

- **缓存键**: `vlab:cache:list:ipDisallow`
- **缓存时间**: 5分钟
- **缓存内容**: 所有启用状态的IP黑名单配置
- **更新策略**: 创建/更新/删除时自动清除缓存
- **降级策略**: 缓存失败时直接查询数据库

### IP格式支持

- **单个IP**: `*************`
- **CIDR网段**: `10.0.0.0/8`, `**********/12`, `***********/16`
- **混合格式**: `*************,10.0.0.0/8,**********/12`

## 测试

### 运行单元测试

```bash
# 测试数据模型
go test ./app/dao/resource_ip_disallow/

# 测试中间件
go test ./app/middleware/

# 测试管理接口
go test ./app/handler/admin/
```

### 集成测试

```bash
# 运行示例代码
go run examples/ip_blacklist_example.go
```

## 监控和日志

### 关键日志

- IP黑名单命中: `INFO Force audit mode enabled due to IP blacklist`
- 缓存失败降级: `WARN Failed to get IP blacklist from cache`
- 配置操作: `INFO IP blacklist created/updated/deleted successfully`

### 监控指标

- IP黑名单命中率
- 缓存命中率
- 查询响应时间
- 错误率统计

## 安全考虑

1. **IP获取安全**: 使用`gin.Context.ClientIP()`获取真实IP，防止伪造
2. **权限控制**: 严格的管理员权限验证和操作日志
3. **输入验证**: 严格验证IP地址和CIDR格式
4. **错误处理**: 不暴露敏感信息，统一错误响应格式

## 性能优化

1. **Redis缓存**: 减少数据库查询，提高响应速度
2. **批量查询**: 一次性加载所有配置到内存
3. **降级策略**: 确保服务在异常情况下的可用性
4. **索引优化**: 数据库表使用适当的索引

## 注意事项

1. 每个渠道只能有一个IP黑名单配置
2. IP列表最大长度为2000字符
3. 配置更新后会自动清除缓存
4. 中间件不会阻断正常请求，只设置强制审核标志
5. 建议定期监控IP黑名单的命中情况

## 后续扩展

- [ ] 支持IP白名单功能
- [ ] 支持时间段限制
- [ ] 支持地理位置过滤
- [ ] 支持动态规则配置
- [ ] 支持批量导入/导出
