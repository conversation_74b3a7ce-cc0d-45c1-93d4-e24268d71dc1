-- 关联剧池功能数据表
-- 创建时间：2025-01-04

-- 关联组表
CREATE TABLE IF NOT EXISTS `content_show_relation_group` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(255) DEFAULT NULL COMMENT '关联组名称',
  `description` text COMMENT '关联组描述',
  `relation_type` tinyint unsigned DEFAULT '1' COMMENT '关联类型: 1-相关推荐,2-同系列,3-相似题材,4-多语言组合(I18n)',
  `status` tinyint unsigned DEFAULT '1' COMMENT '状态: 1-启用,2-禁用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_relation_type` (`relation_type`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='剧集关联组表';

-- 关联组成员表
CREATE TABLE IF NOT EXISTS `content_show_relation_member` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `group_id` bigint unsigned NOT NULL COMMENT '关联组ID',
  `show_id` bigint unsigned NOT NULL COMMENT '剧ID',
  `order` int unsigned DEFAULT '0' COMMENT '在组内的排序',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_group_show` (`group_id`,`show_id`,`deleted_at`),
  KEY `idx_group_id` (`group_id`),
  KEY `idx_show_id` (`show_id`),
  KEY `idx_order` (`order`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='剧集关联组成员表';

-- 添加示例数据（可选）
-- relation_type 对应关系: 1-相关推荐, 2-同系列, 3-相似题材, 4-多语言组合(I18n)
-- INSERT INTO `content_show_relation_group` (`name`, `description`, `relation_type`, `status`) VALUES
-- ('漫威电影宇宙', '包含所有漫威MCU系列电影', 2, 1),
-- ('DC电影系列', '包含所有DC相关电影', 2, 1),
-- ('科幻惊悚推荐', '相似题材的科幻惊悚电影', 3, 1);

-- 为后续扩展预留的索引
-- ALTER TABLE `content_show_relation_member` ADD INDEX `idx_group_show_order` (`group_id`, `show_id`, `order`);
