-- 剧集外部ID映射表
-- 创建时间：2025-01-12
-- 功能：存储content_show与外部平台（IMDB、TMDB、Trakt）的ID映射关系

CREATE TABLE IF NOT EXISTS `content_show_external_ids` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `show_id` bigint unsigned NOT NULL COMMENT '关联content_show表的ID',
  `trakt_id` int unsigned DEFAULT NULL COMMENT 'Trakt平台ID',
  `slug` varchar(255) DEFAULT NULL COMMENT 'Slug标识符（如：spider-man-2002）',
  `imdb_id` varchar(20) DEFAULT NULL COMMENT 'IMDB ID（如：tt0145487）',
  `tmdb_id` int unsigned DEFAULT NULL COMMENT 'TMDB（The Movie Database）ID',
  `match_type` varchar(10) DEFAULT NULL COMMENT '匹配类型：movie-电影，show-电视剧',
  `match_score` decimal(5,2) DEFAULT NULL COMMENT '匹配分数（0-100），90分以上为有效匹配',
  `match_reason` text COMMENT '匹配理由说明',
  `is_match` tinyint(1) DEFAULT 1 COMMENT '是否有效匹配：1-是，0-否',
  `source` varchar(50) DEFAULT 'imdb_api' COMMENT '数据来源：imdb_api-IMDB API，manual-手动录入',
  `raw_response` json DEFAULT NULL COMMENT '原始API响应数据（JSON格式）',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint unsigned DEFAULT 0 COMMENT '软删除标记：0-正常，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_show_id` (`show_id`,`is_deleted`) COMMENT '确保每个剧集只有一条有效记录',
  KEY `idx_imdb_id` (`imdb_id`) COMMENT 'IMDB ID索引',
  KEY `idx_tmdb_id` (`tmdb_id`) COMMENT 'TMDB ID索引',
  KEY `idx_trakt_id` (`trakt_id`) COMMENT 'Trakt ID索引',
  KEY `idx_slug` (`slug`) COMMENT 'Slug索引',
  KEY `idx_match_type` (`match_type`) COMMENT '匹配类型索引',
  KEY `idx_is_deleted` (`is_deleted`) COMMENT '软删除索引',
  KEY `idx_created_at` (`created_at`) COMMENT '创建时间索引',
  KEY `idx_show_id_is_deleted` (`show_id`, `is_deleted`) COMMENT '联合索引优化查询'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='剧集外部ID映射表';

-- 添加表注释
ALTER TABLE `content_show_external_ids` COMMENT='剧集外部ID映射表：存储VLab剧集与IMDB、TMDB、Trakt等外部平台的ID映射关系，用于数据同步和内容匹配';

-- 示例数据（可选，用于测试）
-- INSERT INTO `content_show_external_ids` 
-- (`show_id`, `trakt_id`, `slug`, `imdb_id`, `tmdb_id`, `match_type`, `match_score`, `match_reason`, `is_match`) 
-- VALUES 
-- (1, 438, 'spider-man-2002', 'tt0145487', 557, 'movie', 100.00, '标题完全匹配，年份一致', 1),
-- (2, 1390, 'breaking-bad', 'tt0903747', 1396, 'show', 98.50, '标题匹配，类型一致，剧情相似', 1);