package examples

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"vlab/app/dao/resource_ip_disallow"
	"vlab/app/middleware"

	"github.com/gin-gonic/gin"
)

// IPBlacklistExample IP黑名单使用示例
func IPBlacklistExample() {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 创建路由
	router := gin.New()

	// 模拟设置渠道ID的中间件
	router.Use(func(ctx *gin.Context) {
		// 在实际应用中，这个值通常从请求参数、头部或JWT中获取
		ctx.Set("channelID", uint64(1))
		ctx.Next()
	})

	// 添加IP黑名单检查中间件
	router.Use(middleware.IPBlacklistCheck())

	// 定义测试路由
	router.GET("/api/show/list", func(ctx *gin.Context) {
		// 检查是否需要强制审核
		forceAudit := middleware.GetForceAuditStatus(ctx)

		if forceAudit {
			// 强制审核模式：返回审核中的内容
			ctx.JSON(http.StatusOK, gin.H{
				"code":    200,
				"message": "success",
				"data": gin.H{
					"mode":  "audit",
					"shows": []string{"审核中的剧集1", "审核中的剧集2"},
				},
			})
		} else {
			// 正常模式：返回正常内容
			ctx.JSON(http.StatusOK, gin.H{
				"code":    200,
				"message": "success",
				"data": gin.H{
					"mode":  "normal",
					"shows": []string{"正常剧集1", "正常剧集2", "正常剧集3"},
				},
			})
		}
	})

	// 模拟不同IP的请求
	testIPs := []string{
		"*************", // 假设这个IP在黑名单中
		"*************", // 假设这个IP不在黑名单中
		"*********",     // 假设这个IP在CIDR范围内
		"***********",   // 假设这个IP不在黑名单中
	}

	for _, ip := range testIPs {
		fmt.Printf("\n=== 测试IP: %s ===\n", ip)

		// 创建请求
		req, _ := http.NewRequest("GET", "/api/show/list", nil)
		req.Header.Set("X-Real-IP", ip)

		// 创建响应记录器
		w := httptest.NewRecorder()

		// 执行请求
		router.ServeHTTP(w, req)

		// 输出结果
		fmt.Printf("状态码: %d\n", w.Code)
		fmt.Printf("响应体: %s\n", w.Body.String())
	}
}

// DatabaseConfigExample 数据库配置示例
func DatabaseConfigExample() {
	fmt.Println("=== IP黑名单数据库配置示例 ===")

	// 示例1：为渠道1配置IP黑名单
	config1 := &resource_ip_disallow.Model{
		ChannelID: 1,
		IPList:    "*************,10.0.0.0/8,**********/12", // 混合格式
		Status:    1,                                        // 启用
	}
	fmt.Printf("渠道1配置: %+v\n", config1)

	// 示例2：为渠道2配置IP黑名单
	config2 := &resource_ip_disallow.Model{
		ChannelID: 2,
		IPList:    "***********/24,************/24", // 仅CIDR格式
		Status:    1,                                // 启用
	}
	fmt.Printf("渠道2配置: %+v\n", config2)

	// 示例3：测试IP匹配
	testCases := []struct {
		config   *resource_ip_disallow.Model
		clientIP string
		expected bool
	}{
		{config1, "*************", true},  // 精确匹配
		{config1, "*********", true},      // CIDR匹配
		{config1, "**************", true}, // CIDR匹配
		{config1, "***********", false},   // 不匹配
		{config2, "************", true},   // CIDR匹配
		{config2, "***********", false},   // 不匹配
	}

	fmt.Println("\n=== IP匹配测试 ===")
	for i, tc := range testCases {
		result := tc.config.IsIPInBlacklist(tc.clientIP)
		status := "✓"
		if result != tc.expected {
			status = "✗"
		}
		fmt.Printf("测试%d %s: IP %s 在渠道%d中 -> %v (期望: %v)\n",
			i+1, status, tc.clientIP, tc.config.ChannelID, result, tc.expected)
	}
}

// MiddlewareIntegrationExample 中间件集成示例
func MiddlewareIntegrationExample() {
	fmt.Println("\n=== 中间件集成示例 ===")

	// 创建路由组
	router := gin.New()

	// 全局中间件
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// API路由组
	api := router.Group("/api")

	// 用户认证中间件（示例）
	api.Use(func(ctx *gin.Context) {
		// 模拟用户认证
		ctx.Set("userID", uint64(12345))
		ctx.Set("channelID", uint64(1))
		ctx.Next()
	})

	// IP黑名单检查中间件
	api.Use(middleware.IPBlacklistCheck())

	// 业务路由
	show := api.Group("/show")
	{
		show.GET("/list", func(ctx *gin.Context) {
			forceAudit := middleware.GetForceAuditStatus(ctx)
			ctx.JSON(http.StatusOK, gin.H{
				"force_audit": forceAudit,
				"message":     "剧集列表",
			})
		})

		show.GET("/detail", func(ctx *gin.Context) {
			forceAudit := middleware.GetForceAuditStatus(ctx)
			ctx.JSON(http.StatusOK, gin.H{
				"force_audit": forceAudit,
				"message":     "剧集详情",
			})
		})
	}

	fmt.Println("路由配置完成，中间件执行顺序：")
	fmt.Println("1. gin.Logger() - 日志记录")
	fmt.Println("2. gin.Recovery() - 异常恢复")
	fmt.Println("3. 用户认证中间件 - 设置用户信息和渠道ID")
	fmt.Println("4. middleware.IPBlacklistCheck() - IP黑名单检查")
	fmt.Println("5. 业务处理器 - 处理具体业务逻辑")
}

// PerformanceOptimizationExample 性能优化示例
func PerformanceOptimizationExample() {
	fmt.Println("\n=== 性能优化示例 ===")

	fmt.Println("1. 缓存策略:")
	fmt.Println("   - Redis缓存IP黑名单配置，减少数据库查询")
	fmt.Println("   - 缓存时间：5分钟")
	fmt.Println("   - 缓存键：vlab:cache:list:ipDisallow")

	fmt.Println("\n2. 查询优化:")
	fmt.Println("   - 使用渠道ID索引快速查询")
	fmt.Println("   - 批量加载所有启用的配置到内存")
	fmt.Println("   - 使用map结构快速查找渠道配置")

	fmt.Println("\n3. 错误处理:")
	fmt.Println("   - 缓存失败时降级到数据库查询")
	fmt.Println("   - 查询失败时不阻断正常请求")
	fmt.Println("   - 记录详细日志便于排查问题")

	fmt.Println("\n4. 监控指标:")
	fmt.Println("   - IP黑名单命中率")
	fmt.Println("   - 缓存命中率")
	fmt.Println("   - 查询响应时间")
	fmt.Println("   - 错误率统计")
}

// AdminAPIExample 管理API使用示例
func AdminAPIExample() {
	fmt.Println("\n=== 管理API使用示例 ===")

	// 示例1：创建IP黑名单配置
	fmt.Println("1. 创建IP黑名单配置:")
	fmt.Println("POST /admin/ip-blacklist/create")
	fmt.Println(`{
  "channel_id": 1,
  "ip_list": "*************,10.0.0.0/8,**********/12",
  "status": 1
}`)

	// 示例2：查询IP黑名单列表
	fmt.Println("\n2. 查询IP黑名单列表:")
	fmt.Println("GET /admin/ip-blacklist/list?page=1&limit=20&channel_id=1")

	// 示例3：测试IP是否在黑名单中
	fmt.Println("\n3. 测试IP是否在黑名单中:")
	fmt.Println("GET /admin/ip-blacklist/test?channel_id=1&ip=*************")

	// 示例4：更新IP黑名单配置
	fmt.Println("\n4. 更新IP黑名单配置:")
	fmt.Println("PUT /admin/ip-blacklist/update")
	fmt.Println(`{
  "id": 1,
  "channel_id": 1,
  "ip_list": "*************,*************,10.0.0.0/8",
  "status": 1
}`)

	// 示例5：删除IP黑名单配置
	fmt.Println("\n5. 删除IP黑名单配置:")
	fmt.Println("DELETE /admin/ip-blacklist/delete?id=1")
}

// CacheManagementExample 缓存管理示例
func CacheManagementExample() {
	fmt.Println("\n=== 缓存管理示例 ===")

	fmt.Println("缓存策略:")
	fmt.Println("- 缓存键: vlab:cache:list:ipDisallow")
	fmt.Println("- 缓存时间: 5分钟")
	fmt.Println("- 缓存内容: 所有启用状态的IP黑名单配置")

	fmt.Println("\n缓存更新时机:")
	fmt.Println("- 创建新配置时自动清除缓存")
	fmt.Println("- 更新配置时自动清除缓存")
	fmt.Println("- 删除配置时自动清除缓存")

	fmt.Println("\n缓存降级策略:")
	fmt.Println("- 缓存获取失败时，直接查询数据库")
	fmt.Println("- 数据库查询失败时，不阻断正常请求")
	fmt.Println("- 记录详细错误日志便于排查")
}

// SecurityConsiderationsExample 安全考虑示例
func SecurityConsiderationsExample() {
	fmt.Println("\n=== 安全考虑示例 ===")

	fmt.Println("1. IP获取安全:")
	fmt.Println("   - 使用gin.Context.ClientIP()获取真实IP")
	fmt.Println("   - 考虑X-Real-IP、X-Forwarded-For等头部")
	fmt.Println("   - 防止IP伪造攻击")

	fmt.Println("\n2. 权限控制:")
	fmt.Println("   - 所有管理接口需要管理员登录")
	fmt.Println("   - 使用CheckAccountAuth()进行权限验证")
	fmt.Println("   - 记录管理员操作日志")

	fmt.Println("\n3. 输入验证:")
	fmt.Println("   - 严格验证IP地址和CIDR格式")
	fmt.Println("   - 限制IP列表长度（最大2000字符）")
	fmt.Println("   - 防止SQL注入和XSS攻击")

	fmt.Println("\n4. 错误处理:")
	fmt.Println("   - 不暴露敏感的系统信息")
	fmt.Println("   - 统一的错误响应格式")
	fmt.Println("   - 详细的服务端日志记录")
}
