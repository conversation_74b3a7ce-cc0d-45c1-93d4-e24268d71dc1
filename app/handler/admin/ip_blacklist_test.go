package admin

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"vlab/app/dto/admin"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestValidateIPList(t *testing.T) {
	tests := []struct {
		name    string
		ipList  string
		wantErr bool
	}{
		{
			name:    "empty ip list",
			ipList:  "",
			wantErr: true,
		},
		{
			name:    "valid single ip",
			ipList:  "*************",
			wantErr: false,
		},
		{
			name:    "valid multiple ips",
			ipList:  "*************,********",
			wantErr: false,
		},
		{
			name:    "valid cidr",
			ipList:  "***********/24",
			wantErr: false,
		},
		{
			name:    "valid mixed format",
			ipList:  "*************,10.0.0.0/8,**********",
			wantErr: false,
		},
		{
			name:    "invalid ip",
			ipList:  "invalid-ip",
			wantErr: true,
		},
		{
			name:    "invalid cidr",
			ipList:  "***********/33",
			wantErr: true,
		},
		{
			name:    "mixed valid and invalid",
			ipList:  "*************,invalid-ip",
			wantErr: true,
		},
		{
			name:    "with spaces",
			ipList:  "*************, 10.0.0.0/8 , **********",
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateIPList(tt.ipList)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestAdminIPBlacklistHandlers(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// 创建测试路由
	router := gin.New()
	
	// 模拟认证中间件
	router.Use(func(ctx *gin.Context) {
		ctx.Set("accountID", uint64(1))
		ctx.Next()
	})

	// 注册路由
	router.GET("/admin/ip-blacklist/list", AdminIPBlacklistList)
	router.GET("/admin/ip-blacklist/detail", AdminIPBlacklistDetail)
	router.POST("/admin/ip-blacklist/create", AdminIPBlacklistCreate)
	router.PUT("/admin/ip-blacklist/update", AdminIPBlacklistUpdate)
	router.DELETE("/admin/ip-blacklist/delete", AdminIPBlacklistDelete)
	router.GET("/admin/ip-blacklist/test", AdminIPBlacklistTest)

	t.Run("AdminIPBlacklistList", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/admin/ip-blacklist/list?page=1&limit=10", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// 由于没有数据库连接，这里主要测试参数绑定
		// 在实际环境中需要mock数据库
		assert.Contains(t, []int{200, 500}, w.Code) // 可能成功或数据库错误
	})

	t.Run("AdminIPBlacklistDetail", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/admin/ip-blacklist/detail?id=1", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Contains(t, []int{200, 404, 500}, w.Code)
	})

	t.Run("AdminIPBlacklistCreate", func(t *testing.T) {
		createReq := admin.AdminIPBlacklistCreateReq{
			ChannelID: 1,
			IPList:    "*************,10.0.0.0/8",
			Status:    1,
		}
		
		jsonData, _ := json.Marshal(createReq)
		req, _ := http.NewRequest("POST", "/admin/ip-blacklist/create", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Contains(t, []int{200, 500}, w.Code)
	})

	t.Run("AdminIPBlacklistCreate_InvalidIP", func(t *testing.T) {
		createReq := admin.AdminIPBlacklistCreateReq{
			ChannelID: 1,
			IPList:    "invalid-ip",
			Status:    1,
		}
		
		jsonData, _ := json.Marshal(createReq)
		req, _ := http.NewRequest("POST", "/admin/ip-blacklist/create", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, 400, w.Code) // 参数错误
	})

	t.Run("AdminIPBlacklistUpdate", func(t *testing.T) {
		updateReq := admin.AdminIPBlacklistUpdateReq{
			ID:        1,
			ChannelID: 1,
			IPList:    "*************,10.0.0.0/8",
			Status:    0,
		}
		
		jsonData, _ := json.Marshal(updateReq)
		req, _ := http.NewRequest("PUT", "/admin/ip-blacklist/update", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Contains(t, []int{200, 404, 500}, w.Code)
	})

	t.Run("AdminIPBlacklistDelete", func(t *testing.T) {
		req, _ := http.NewRequest("DELETE", "/admin/ip-blacklist/delete?id=1", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Contains(t, []int{200, 404, 500}, w.Code)
	})

	t.Run("AdminIPBlacklistTest", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/admin/ip-blacklist/test?channel_id=1&ip=*************", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Contains(t, []int{200, 500}, w.Code)
	})

	t.Run("AdminIPBlacklistTest_InvalidIP", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/admin/ip-blacklist/test?channel_id=1&ip=invalid-ip", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, 400, w.Code) // 参数错误
	})
}
