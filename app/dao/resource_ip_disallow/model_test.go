package resource_ip_disallow

import (
	"testing"
	"vlab/app/common/dbs"

	"github.com/stretchr/testify/assert"
)

func TestModel_GetIPCIDRList(t *testing.T) {
	tests := []struct {
		name     string
		ipList   string
		expected []string
	}{
		{
			name:     "empty ip list",
			ipList:   "",
			expected: []string{},
		},
		{
			name:     "single ip",
			ipList:   "*************",
			expected: []string{"*************"},
		},
		{
			name:     "multiple ips",
			ipList:   "*************,********,**********",
			expected: []string{"*************", "********", "**********"},
		},
		{
			name:     "cidr format",
			ipList:   "***********/24,10.0.0.0/8",
			expected: []string{"***********/24", "10.0.0.0/8"},
		},
		{
			name:     "mixed format with spaces",
			ipList:   "*************, 10.0.0.0/8 , **********",
			expected: []string{"*************", "10.0.0.0/8", "**********"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Model{IPList: tt.ipList}
			result := m.GetIPCIDRList()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestModel_IsIPInBlacklist(t *testing.T) {
	tests := []struct {
		name     string
		model    *Model
		clientIP string
		expected bool
	}{
		{
			name: "disabled status",
			model: &Model{
				Status: uint32(dbs.StatusDisable),
				IPList: "*************",
			},
			clientIP: "*************",
			expected: false,
		},
		{
			name: "empty ip list",
			model: &Model{
				Status: uint32(dbs.StatusEnable),
				IPList: "",
			},
			clientIP: "*************",
			expected: false,
		},
		{
			name: "exact ip match",
			model: &Model{
				Status: uint32(dbs.StatusEnable),
				IPList: "*************",
			},
			clientIP: "*************",
			expected: true,
		},
		{
			name: "ip not in list",
			model: &Model{
				Status: uint32(dbs.StatusEnable),
				IPList: "*************",
			},
			clientIP: "*************",
			expected: false,
		},
		{
			name: "cidr match",
			model: &Model{
				Status: uint32(dbs.StatusEnable),
				IPList: "***********/24",
			},
			clientIP: "*************",
			expected: true,
		},
		{
			name: "cidr no match",
			model: &Model{
				Status: uint32(dbs.StatusEnable),
				IPList: "***********/24",
			},
			clientIP: "*************",
			expected: false,
		},
		{
			name: "multiple ips with match",
			model: &Model{
				Status: uint32(dbs.StatusEnable),
				IPList: "*************,********,**********",
			},
			clientIP: "********",
			expected: true,
		},
		{
			name: "mixed format with cidr match",
			model: &Model{
				Status: uint32(dbs.StatusEnable),
				IPList: "*************,10.0.0.0/8,**********",
			},
			clientIP: "*********",
			expected: true,
		},
		{
			name: "mixed format with exact match",
			model: &Model{
				Status: uint32(dbs.StatusEnable),
				IPList: "*************,10.0.0.0/8,**********",
			},
			clientIP: "**********",
			expected: true,
		},
		{
			name: "invalid client ip",
			model: &Model{
				Status: uint32(dbs.StatusEnable),
				IPList: "*************",
			},
			clientIP: "invalid-ip",
			expected: false,
		},
		{
			name: "invalid cidr in list",
			model: &Model{
				Status: uint32(dbs.StatusEnable),
				IPList: "*************,invalid-cidr,**********",
			},
			clientIP: "**********",
			expected: true,
		},
		{
			name: "large cidr block",
			model: &Model{
				Status: uint32(dbs.StatusEnable),
				IPList: "10.0.0.0/8",
			},
			clientIP: "**************",
			expected: true,
		},
		{
			name: "private network ranges",
			model: &Model{
				Status: uint32(dbs.StatusEnable),
				IPList: "10.0.0.0/8,**********/12,***********/16",
			},
			clientIP: "**************",
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.model.IsIPInBlacklist(tt.clientIP)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestModelList_GetChannelMap(t *testing.T) {
	list := ModelList{
		{ID: 1, ChannelID: 1, IPList: "*************"},
		{ID: 2, ChannelID: 2, IPList: "10.0.0.0/8"},
		{ID: 3, ChannelID: 3, IPList: "**********"},
	}

	channelMap := list.GetChannelMap()

	assert.Len(t, channelMap, 3)
	assert.Equal(t, uint64(1), channelMap[1].ID)
	assert.Equal(t, uint64(1), channelMap[1].ChannelID)
	assert.Equal(t, "*************", channelMap[1].IPList)

	assert.Equal(t, uint64(2), channelMap[2].ID)
	assert.Equal(t, uint64(2), channelMap[2].ChannelID)
	assert.Equal(t, "10.0.0.0/8", channelMap[2].IPList)
}

func TestModelList_GetIDMap(t *testing.T) {
	list := ModelList{
		{ID: 1, ChannelID: 1, IPList: "*************"},
		{ID: 2, ChannelID: 2, IPList: "10.0.0.0/8"},
	}

	idMap := list.GetIDMap()

	assert.Len(t, idMap, 2)
	assert.Equal(t, uint64(1), idMap[1].ChannelID)
	assert.Equal(t, uint64(2), idMap[2].ChannelID)
}
