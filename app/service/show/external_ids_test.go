package show

import (
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	showDao "vlab/app/dao/content_show"
)

func TestExtractYear(t *testing.T) {
	service := &ExternalIDsService{}

	tests := []struct {
		name     string
		show     *showDao.Show
		expected int
	}{
		{
			name: "使用PresentationTime",
			show: &showDao.Show{
				PresentationTime: 2023,
			},
			expected: 2023,
		},
		{
			name: "使用AirDate",
			show: &showDao.Show{
				AirDate: "2022-01-01",
			},
			expected: 2022,
		},
		{
			name: "使用AirDateTs",
			show: &showDao.Show{
				AirDateTs: func() *time.Time {
					t := time.Date(2021, 6, 15, 0, 0, 0, 0, time.UTC)
					return &t
				}(),
			},
			expected: 2021,
		},
		{
			name:     "无年份信息返回0",
			show:     &showDao.Show{},
			expected: 0,
		},
		{
			name: "无效AirDate返回0",
			show: &showDao.Show{
				AirDate: "invalid",
			},
			expected: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.extractYear(tt.show)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestExtractLanguage(t *testing.T) {
	service := &ExternalIDsService{}

	tests := []struct {
		name     string
		show     *showDao.Show
		expected string
	}{
		{
			name: "单个语言",
			show: &showDao.Show{
				Langs: "zh",
			},
			expected: "zh",
		},
		{
			name: "多个语言取第一个",
			show: &showDao.Show{
				Langs: "zh,en,ja",
			},
			expected: "zh",
		},
		{
			name: "空语言返回默认en",
			show: &showDao.Show{
				Langs: "",
			},
			expected: "en",
		},
		{
			name: "带空格的语言",
			show: &showDao.Show{
				Langs: " fr , de ",
			},
			expected: "fr",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.extractLanguage(tt.show)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestDetermineContentType(t *testing.T) {
	service := &ExternalIDsService{}

	tests := []struct {
		name     string
		show     *showDao.Show
		expected string
	}{
		{
			name: "电影类型",
			show: &showDao.Show{
				ContentType: showDao.ContentTypeMovie,
			},
			expected: "movie",
		},
		{
			name: "电视剧类型",
			show: &showDao.Show{
				ContentType: showDao.ContentTypeTV,
			},
			expected: "show",
		},
		{
			name: "动漫类型",
			show: &showDao.Show{
				ContentType: showDao.ContentTypeComic,
			},
			expected: "auto",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.determineContentType(tt.show)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestExtractGenres 测试genre提取
// 注意：这个测试需要mock数据库连接，这里只是示例
func TestExtractGenresMock(t *testing.T) {
	// 创建gin context
	gin.SetMode(gin.TestMode)
	c, _ := gin.CreateTestContext(nil)

	service := &ExternalIDsService{}
	show := &showDao.Show{}
	show.ID = 1

	// 在实际环境中，这会查询数据库
	// 这里我们只测试方法是否能正确处理错误情况
	genres := service.extractGenres(c, show)
	
	// 当没有数据时应该返回unknown
	assert.Equal(t, []string{"unknown"}, genres)
}