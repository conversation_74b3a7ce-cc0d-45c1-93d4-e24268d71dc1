package middleware

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestGetForceAuditStatus(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name     string
		setValue interface{}
		expected bool
	}{
		{
			name:     "force audit true",
			setValue: true,
			expected: true,
		},
		{
			name:     "force audit false",
			setValue: false,
			expected: false,
		},
		{
			name:     "no value set",
			setValue: nil,
			expected: false,
		},
		{
			name:     "invalid type",
			setValue: "invalid",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			ctx, _ := gin.CreateTestContext(w)

			if tt.setValue != nil {
				ctx.Set(ForceAuditStatusKey, tt.setValue)
			}

			result := GetForceAuditStatus(ctx)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestSetForceAuditStatus(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name     string
		setValue bool
		expected bool
	}{
		{
			name:     "set true",
			setValue: true,
			expected: true,
		},
		{
			name:     "set false",
			setValue: false,
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			ctx, _ := gin.CreateTestContext(w)

			SetForceAuditStatus(ctx, tt.setValue)
			result := GetForceAuditStatus(ctx)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestIPBlacklistCheckMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// 创建测试路由
	router := gin.New()
	
	// 模拟设置渠道ID的中间件
	router.Use(func(ctx *gin.Context) {
		ctx.Set("channelID", uint64(1))
		ctx.Next()
	})
	
	router.Use(IPBlacklistCheck())
	
	router.GET("/test", func(ctx *gin.Context) {
		forceAudit := GetForceAuditStatus(ctx)
		ctx.JSON(http.StatusOK, gin.H{
			"force_audit": forceAudit,
		})
	})

	tests := []struct {
		name           string
		clientIP       string
		expectedStatus int
		setupMock      func()
	}{
		{
			name:           "normal request",
			clientIP:       "***********",
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.setupMock != nil {
				tt.setupMock()
			}

			req, _ := http.NewRequest("GET", "/test", nil)
			req.Header.Set("X-Real-IP", tt.clientIP)
			
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}
